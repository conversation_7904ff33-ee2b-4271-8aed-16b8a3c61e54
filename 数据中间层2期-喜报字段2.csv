﻿模块,分类,字段名,BC端中间层指标@崔峻(熊鹿)(崔峻),六夏标记-是否需要同行对比,小数位,端,时间周期（T+1）,汇总维度,多门店维度是否可加总,"门店维度默认展示
用于喜报页面展示","PID维度默认展示
用于批量发喜报展示",对应经营参谋指标,字段说明（经营喜报小问号）,业务口径,喜报字段-服务端--值,喜报字段-服务端--环比,喜报底表-数仓,上游表,数仓底表对应字段,最大可支持的时间跨度,,,,,,,,,,,,,,,,,,,,
商家质量分,商家质量分,商家质量分,,1,,高德,实时API,门店,/,,/,,,高德商家门店质量分,"business_score

",,,,,最新的,,,,,,,,,,,,,,,,,,,,
门店信息守护,/,防干扰（天）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,是,是,是,无,,,"wp_online_days

",,,,,,,,,,,,,,,,,,,,,,,,,
累计年费在约（天）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,否,是,是,无,,,"wp_online_days_total

",,,,,,,,,,,,,,,,,,,,,,,,,
信息安全守护（次）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,是,是,是,累计用户反馈（经营参谋app端）,,,"ecurity_guards_cnt
",,,,,,,,,,,,,,,,,,,,,,,,,
高德独家数据,/,导航搜索量——>门店曝光量,【分发域】里的——门店曝光量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,曝光量,顾客在高德地图上看到门店的次数（包含顾客使用地图、搜索等功能时看到该店的次数）,门店在高德上的所有曝光pv，包含广告（客资通+到店通）及活动推广带来曝光量,exposure_pv,exposure_pv_last_cycle,exposure_pv,ads_gd_biz_shop_good_news_index_90d,exp_pv,"广告表可以回溯15个月@陈擎霄(淮序)
曝光指标20220801之后 OK 有开发量",,,,,,,,,,,,,,,,,,,,
驾车导航量,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,驾车导航量,通过驾车方式（驾车、新能源、货车、摩托车）发起路线规划、导航的总量（终点为当前poi）,通过驾车方式（驾车、新能源、货车、摩托车）发起路线规划、导航的总量（终点为当前poi）,,,,,,,,,,,,,,,,,,,,,,,,,,
导航到店量,【流量埋点】里的——导航到店,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,导航到店量,顾客在高德地图发起店铺导航的数量,用户使用高德app导航到门店的人次,route_pv,route_pv_last_cycle,route_pv,ads_gd_biz_shop_good_news_index_90d,navi_pv,（4个月，近似计算@崔峻(熊鹿)(崔峻)）,,,,,,,,,,,,,,,,,,,,
"经营数据
",/,商品数,【商家域】里的——上架商品数量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,是,是,上架商品数,门店在线商品的总数量,门店在线商品的总数量，包含套餐、代金券、次卡、券包，（商户维度去重）,spu_cnt,spu_cnt_last_cycle,spu_cnt,mds_kb_shop_supply_ds,online_item_cnt,已经满足@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
商家相册数,【商家域】里的——相册图片数量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,,,"PID,门店",是,是,是,无,商家上传的相册数,,shop_pic_cnt,,,,,,,,,,,,,,,,,,,,,,,,,
成交订单数,【交易域】里的——商品成交订单量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,售卖单量,门店的成交笔数，包括高德、口碑、支付宝端,多端的店铺的成交笔数,"order_cnt

","order_cnt

_last_cycle","order_cnt

",dws_kb_trd_shop_1d,pay_suborder_cnt_1d,已经满足@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
成交金额,【交易域】里的——商品成交订单金额,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,成交金额,门店的成交总金额，包括高德、口碑、支付宝端,多端的店铺的成交金额,"order_price

","order_price

_last_cycle","order_price
",dws_kb_trd_shop_1d,gmv,已经满足@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
核销金额——>商品核销金额,【交易域】里的——核销订单金额,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,核销金额,门店的核销总金额，包括高德、口碑、支付宝端,多端的店铺的核销金额,"used_order_price

","used_order_price
_last_cycle","used_order_price
",dws_kb_trd_shop_1d,use_gmv,已经满足@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
核销笔数——>商品核销量,【交易域】里的——核销订单量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,核销单量,门店的核销笔数，包括高德、口碑、支付宝端,多端的店铺的核销笔数,"used_order_cnt

","used_order_cnt

_last_cycle","used_order_cnt


",dws_kb_trd_shop_item_channel_ordertype_online_live_third_1d,use_suborder_cnt_1d,已经满足@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
电话拨打量,【流量埋点】里的——电话拨打量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,否,无,拨打门店电话的次数（不限制必须接通）【目前已有poi的电话量】,拨打门店电话的次数（不限制必须接通）【目前已有poi的电话量】,"phone_call_order_cnt

",,"phone_call_order_cnt
",,,,,,,,,,,,,,,,,,,,,,,
电话接通量（现在没有，需要增加）,【广告域】里的——电话接通量,1,,,,,,,,,,,"phone_order_cnt

",,,,,,,,,,,,,,,,,,,,,,,,,
电话接通率（现在没有，需要增加）,电话接通量/电话拨打量,1,,,,,,,,,,,phone_order_rate,phone_order_rate_last_cycle,,,,,,,,,,,,,,,,,,,,,,,,
新增评论数,【分发域】里的——当日新增评价数,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,新增评论数（下载表格里）,在高德、口碑、支付宝展示的新增评论数量,在高德、口碑、支付宝展示的新增评论数量,review_cnt,review_cnt_last_cycle,review_cnt,"dwd_ot_log_shop_comment_dd
ads_gd_biz_shop_good_news_index_90d","kb_comment_cnt
new_comment_cnt",回溯15个月,,,,,,,,,,,,,,,,,,,,
广告数据,流量及消耗@陈擎霄(淮序),广告曝光量,c,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,投放广告增加的门店被看见次数，包括用户到店和客资线索投放产品,投放广告增加的店铺曝光次数（pv），包括用户到店和客资线索投放产品,valid_exposure_cnt,valid_exposure_cnt_last_cycle,ad_exp_pv,amap_ad.dws_gd_ad_flow_effect_base_di,valid_exposure_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
广告日均曝光量,广告曝光量/选择时间段内的投放天数,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,否,无,广告投放期间，平均每天门店被看见的次数（广告曝光量/投放天数）,广告投放期间，用户平均每天看到门店的次数（广告曝光量/投放天数）,valid_exposure_cnt_per_day,valid_exposure_cnt_per_day_last_cycle,ad_exp_pv/天,amap_ad.dws_gd_ad_flow_effect_base_di,valid_exposure_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
广告点击量,【广告域】里的——广告点击量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,投放广告增加的门店被访问次数，包括用户到店和客资线索投放产品,投放广告增加的门店点击次数（pv），包括用户到店和客资线索投放产品,"valid_click_cnt

","valid_click_cnt
_last_cycle",ad_clk_pv,amap_ad.dws_gd_ad_flow_effect_base_di,valid_click_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
广告点击率,广告点击量/广告曝光量,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,广告点击量/广告曝光量,广告点击量/广告曝光量,valid_click_rate,valid_click_rate_last_cycle,（ad_clk_pv/ad_exp_pv）,amap_ad.dws_gd_ad_flow_effect_base_di,valid_click_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
千次曝光成本,（广告总消耗/广告曝光量）*1000,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,否,否,无,每千次曝光产生的花费金额，（（广告总消耗/广告曝光量）*1000）,每千次曝光产生的花费金额，（（广告总消耗/广告曝光量）*1000）,"settle_cost
_per_thousand","settle_cost
_per_thousand_last_cycle",（ad_cust_res_cost/ ad_exp_pv）,amap_ad.dws_gd_ad_flow_effect_base_di,settle_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
日均现金消耗,现金消耗/选择时间段内的投放天数,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,否,是,无,广告投放的日均现金消耗，（现金消耗/投放天数）,广告投放的日均现金消耗（现金消耗/投放天数）,settle_cash_cost_per_day,settle_cash_cost_per_day_last_cycle,（settle_cash_cost/总天数）,amap_ad.dws_gd_ad_flow_effect_base_di,settle_cash_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
广告总消耗,【广告域】里的——广告总消耗,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,指定时间范围内，所选门店广告消耗，包括用户到店和客资线索投放产品,指定时间范围内，所选门店广告消耗，包括用户到店和客资线索投放产品,"settle_cost

","settle_cost


last_cycle","settle_cash_cost


(单位毫分)",amap_ad.dws_gd_ad_flow_effect_base_di,settle_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
现金消耗,【广告域】里的——广告现金消耗,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,否,是,无,广告投放的现金消耗，包括用户到店和客资线索投放产品,广告投放的现金消耗，包括用户到店和客资线索投放产品,settle_cash_cost,settle_cash_cost_last_cycle,settle_cash_cost(单位毫分),amap_ad.dws_gd_ad_flow_effect_base_di,settle_cash_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
红包消耗,【广告域】里的——广告红包消耗,,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,否,是,无,门店和商品广告投放的红包消耗（含现金红包、折扣红包），包括用户到店、客资线索,商户广告投放的红包消耗（含现金红包、折扣红包），包括用户到店、客资线索,"settle_coupon_cost

","settle_coupon_cost


_last_cycle",redp_cost,amap_ad.dws_gd_ad_flow_effect_base_di,settle_coupon_cost + settle_discount_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
客资线索@陈擎霄(淮序),OCPC客资消耗——>客资线索消耗,【广告域】里的——客资线索消耗,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,OCPC客资线索投放产品的广告总消耗（含现金、红包消耗）,OCPC客资线索投放产品的广告总消耗（含现金、红包消耗）,"leads_settle_cost

","leads_settle_cost

last_cycle",ocpc_cost,amap_ad.dws_gd_ad_flow_effect_base_di,product_id = '103301015' THEN settle_cost,今年8月1号上线，无历史数据,,,,,,,,,,,,,,,,,,,,
OCPC客资消耗占比——>客资线索消耗占比,【广告域】里的——客资线索消耗占比,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,OCPC客资线索投放产品的广告总消耗占比（OCPC客资消耗/广告总消耗）,OCPC客资线索投放产品的广告总消耗占比（OCPC客资消耗/广告总消耗）,leads_settle_cost_rate,"leads_settle_cost
_rate_last_cycle",ocpc_cost/ad_cust_res_cost,amap_ad.dws_gd_ad_flow_effect_base_di,product_id = '103301015' THEN settle_cost,今年8月1号上线，无历史数据,,,,,,,,,,,,,,,,,,,,
进店留资率,【广告域】里的——进店留资率,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,用户进入推广门店或商品页后产生留资行为的比例（客资量/客资线索广告点击量）,用户进入推广门店或商品页后产生留资行为的比例（客资量/客资线索广告点击量）,leads_ocpc_sum_cnt_rate,leads_ocpc_sum_cnt_rate_last_cycle,（ad_cust_res_cnt+ leads_cnt_v2）/settle_click_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,"settle_leads_cnt (@陈擎霄(淮序)用leads_cnt补，回溯15个月)
leads_cnt_v2(今年8月1号上线，无历史数据)
settle_click_cnt(今年8月1号上线，无历史数据)",回溯15个月,广告：擎霄,,,,,,,,,,,,,,,,,,,
日均有效客资量——>日均广告客资量,"【广告域】里的——广告客资量
÷选择时间段内的投放天数",1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,否,无,平均每日的有效客资量（有效客资量/投放天数）,平均每日的有效客资量（有效客资量/投放天数）,"bill_leads_cnt
_per_day","bill_leads_cnt
_per_day_per_day_last_cycle",ad_kz_cnt/天,amap_ad.dwd_gd_ad_leads_funnel_di,"COUNT(CASE    WHEN COALESCE(orig_cost,0) <> 0 THEN user_id END)",,,,,,,,,,,,,,,,,,,,,
有效客资成本——>广告客资成本,【广告域】里的——广告客资成本,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,是,是,无,有效单客资的的平均推广成本（OCPC客资消耗/有效客资量）,有效单客资的的平均推广成本（OCPC客资消耗/有效客资量）,leads_settle_cost_consume,ocpc_cost/ad_kz_cnt,ocpc_cost/ad_kz_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,settle_cash_cost,回溯15个月,,,,,,,,,,,,,,,,,,,,
有效客资量——>广告客资量,【广告域】里的——广告客资量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,在历史7天内新客资量（去除重复客资量）,在历史7天内新客资量（去除重复客资量）,"bill_leads_cnt
","bill_leads_cnt
_last_cycle","bill_leads_cnt
",amap_ad.dws_gd_ad_flow_effect_base_di表的settle_leads_cnt,amap_ad.dws_gd_ad_flow_effect_base_di表的leads_cnt+leads_cnt_v2,leads_cnt_v2部分(今年8月1号上线，无历史数据),,,,,,,,,,,,,,,,,,,,
电话客资,【广告域】里的——电话客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，获取的电话咨询有效客资量，包括高德、口碑、支付宝,广告投放带来的电话咨询有效客资量，包括高德、口碑、支付宝端,"bill_call_leads_cnt
","bill_call_leads_cnt_
last_cycle","""bill_call_leads_cnt
""",amap_ad.dws_gd_ad_flow_effect_base_di,call_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
电话客资占比,"【广告域】里的——电话客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,电话咨询客资量占有效客资量的比例（电话客资/有效客资量）,电话咨询客资占有效客资量的占比（电话客资/有效客资量）,"bill_call_leads_cnt
_rate","bill_call_leads_cnt
_rate_last_cycle",call_cust_res_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
平台客资,【广告域】里的——平台客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，获取的有效平台客资量，包括高德、口碑、支付宝,广告投放带来的有效平台客资数量，包括高德、口碑、支付宝端,"bill_platform_leads_cnt
","bill_platform_leads_cnt
_last_cycle",platform_leads_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,platform_leads_cnt,23年11月份上线，无历史数据,,,,,,,,,,,,,,,,,,,,
平台客资占比,"【广告域】里的——平台客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,平台客资量占有效客资量的比例，（平台客资/客资量）,平台客资占有效客资量的占比（平台客资/有效客资量）,"bill_platform_leads_cnt
_rate","bill_platform_leads_cnt
_rate_last_cycle",platform_leads_cnt/ad_kz_cnt,,-,23年11月份上线，无历史数据,,,,,,,,,,,,,,,,,,,,
订单客资,【广告域】里的——订单客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，带来的门店下单有效客资量，包括高德、口碑、支付宝,广告投放带来的订单有效客资数量，包括高德、口碑、支付宝端,"bill_order_leads_cnt
","bill_order_leads_cnt
_last_cycle",ord_cust_res_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,order_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
订单客资占比,"【广告域】里的——订单客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,门店下单的客资量占有效客资量的比例（订单客资/有效客资量）,订单客资占有效客资量的占比（订单客资/有效客资量）,"bill_order_leads_cnt
_rate","bill_order_leads_cnt
_rate_last_cycle",ord_cust_res_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
在线咨询客资,【广告域】里的——在线咨询客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，在高德地图与您发起聊天的顾客数量，，包括高德、口碑、支付宝端,广告投放带来的IM进线咨询后留资的有效客资数量，，包括高德、口碑、支付宝端,"bill_online_consultation_leads_cnt
","bill_online_consultation_leads_cnt
_last_cycle",consult_kz_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,online_consultation_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
在线咨询客资占比,"【广告域】里的——在线咨询客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,在高德地图与您发起聊天的顾客数量占有效客资量的比例（在线咨询客资/有效客资量）,通过IM进线咨询后留资的客资占有效客资量的比例（在线咨询客资/有效客资量）,"bill_online_consultation_leads_cnt
_rate","bill_online_consultation_leads_cnt
_rate_last_cycle",consult_kz_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
预约礼客资,【广告域】里的——预约礼客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，领取到店礼的有效客资量，包括高德、口碑、支付宝端,广告投放后带来的领取到店礼有效留资量，，包括高德、口碑、支付宝端,"bill_shop_reservation_leads_cnt
","bill_shop_reservation_leads_cnt
_last_cycle",reservation_cust_res_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,shop_reservation_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
预约礼客资占比,"【广告域】里的——预约礼客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,领取到店礼的客资量占总客资量的比例（预约礼客资/有效客资量）,领取到店礼留资的客资占客资量的比例（预约礼客资/有效客资量）,"bill_shop_reservation_leads_cnt
_rate","bill_shop_reservation_leads_cnt
_rate_last_cycle",reservation_cust_res_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
到店预约客资,【广告域】里的——到店预约客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，到店预约留资的有效客资量，包括高德、口碑、支付宝端,广告投放带到店预约留资的有效客资量，包括高德、口碑、支付宝端,"bill_arrival_reservation_leads_cnt
","bill_arrival_reservation_leads_cnt
_last_cycle",arrive_kz_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,arrival_reservation_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
到店预约客资占比,"【广告域】里的——到店预约客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,通过广告投放，快速预约留资的客资量占有效客资量的比例（到店预约礼客资/有效客资量）,快速预约留资的客资占有效客资量的比例（到店预约礼客资/有效客资量）,"bill_arrival_reservation_leads_cnt
_rate","bill_arrival_reservation_leads_cnt
_rate_last_cycle",arrive_kz_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
高德打车客资,【广告域】里的——高德打车客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,是,无,通过广告投放，获取的高德打车留资用户量，包括高德、口碑、支付宝端,广告投放带来的高德打车组件留资有效客资数，包括高德、口碑、支付宝端,"bill_gaode_hail_taxis_leads_cnt
","bill_gaode_hail_taxis_leads_cnt
_last_cycle",gd_car_kz_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,gaode_hail_taxis_leads_cnt,回溯15个月,,,,,,,,,,,,,,,,,,,,
高德打车客资占比,"【广告域】里的——高德打车客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,通过广告投放，获取的高德打车留资用户量占有效客资量的比例（高德打车客资/有效客资量）,高德打车组件留资的客资量占有效客资量的比例（高德打车客资/有效客资量）,"bill_gaode_hail_taxis_leads_cnt
_rate","bill_gaode_hail_taxis_leads_cnt
_rate_last_cycle",gd_car_kz_cnt /ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
不可见客资,【广告域】里的——不可见客资,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,PID,否,/,是,无,您在广告未投放期间错过的广告客资数量,广告未投放期间错过的广告客资数量,"invisible_leads_cnt
","invisible_leads_cnt
_last_cycle",invisible_kz_cnt,amap_ad.dwd_gd_ad_leads_funnel_di,COUNT(CASE    WHEN visible <> 1 THEN user_id END) AS invisible_kz_cnt_1d,基于结果的近似计算@李亮(秃贝),,,,,,,,,,,,,,,,,,,,
不可见客资占比,"【广告域】里的——不可见客资
÷【广告域】里的——广告客资量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,PID,否,/,否,无,您在广告未投放期间错过的广告客资数量占客资量的比例（不可见客资量/客资量）,广告未投放期间错过的广告客资数量占客资量的比例（不可见客资/总客资量）,"invisible_leads_cnt
_rate","invisible_leads_cnt
_rate_last_cycle",invisible_kz_cnt/ad_kz_cnt,,-,-,,,,,,,,,,,,,,,,,,,,
分城市客资量占比,您的门店客资量在门店所在城市的占比（门店客资量/门店所在城市的末级atag类目总客资量）,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,门店,否,否,/,无,您的门店客资量在门店所在城市的占比（门店客资量/门店所在城市的二级类目总客资量）,门店客资量/门店所在城市的二级类目总客资量,ad_kz_cnt_per_city,ad_kz_cnt_per_city_last_cycle,一娃确认，删除,,-,-,,,,,,,,,,,,,,,,,,,,
分城市客资成本,您的门店的客资分城市的扣费均价,1,文字：高、较高、低,多端,自定义，最长62天,门店,否,否,/,无,您的门店客资量在门店所在城市的水平（门店客资成本/门店所在城市的二级类目客资成本）,门店客资成本/门店所在城市的二级类目客资成本,city_ad_kz_cost,city_ad_kz_cost_last_cycle,,,-,-,,,,,,,,,,,,,,,,,,,,
分区域客资量占比,您的门店客资量在门店所在行政区的占比（门店客资量/门店所在行政区的末级atag类目总客资量）,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,门店,否,否,/,无,您的门店客资量在门店所在区域的占比（门店客资成本/门店所在区域的二级类目客资成本）,门店客资成本/门店所在区域的二级类目客资成本,ad_kz_cnt_per_district,ad_kz_cnt_per_district_last_cycle,,,-,-,,,,,,,,,,,,,,,,,,,,
分区域客资成本,您的门店的客资分行政区的扣费均价,1,文字：高、较高、低,多端,自定义，最长62天,门店,否,否,/,无,您的门店客资量在门店所在区域的水平（区域客资成本/门店所在区域的二级类目总客资成本）,区域客资成本/门店所在区域的二级类目总客资成本,district_ad_kz_cost,district_ad_kz_cost_last_cycle,,,-,-,,,,,,,,,,,,,,,,,,,,
用户到店@陈擎霄(淮序),CPC到店消耗——>用户到店消耗,【广告域】里的——用户到店消耗,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,通过广告投放，用户点击所产生的消耗金额（含现金、红包消耗）,CPC用户到店投放产品的广告消耗（含现金、红包消耗）,"arrive_settle_cost
","arrive_settle_cost
_last_cycle","""arrive_settle_cost
""",amap_ad.dws_gd_ad_flow_effect_base_di,SUM(CASE    WHEN product_id = '103301029' THEN settle_cost END) AS cpc_cost_1d,回溯15个月,,,,,,,,,,,,,,,,,,,,
CPC到店消耗占比——>用户到店消耗占比,"【广告域】里的——用户到店消耗
÷【广告域】里的——总消耗",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,通过广告投放，用户点击所产生的消耗金额占广告总消耗比例（CPC到店消耗/广告总消耗）,用户到店的广告消耗占广告总消耗比例（CPC到店消耗/广告总消耗）,"arrive_settle_cost
_percent","arrive_settle_cost
_percent_last_cycle",cpc_cost_1d/ad_cost_1d,,,-,,,,,,,,,,,,,,,,,,,,
有效到店量——>广告有效到店量,【广告域】里的——广告有效到店量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,广告投放后，有效到您门店的顾客数，具体包括通过到店导航、到店打车、到店订单核销的行为次数之和,用户到店广告投放后，通过到店导航、打车到店、到店订单核销的用户行为次数之和,"arrive_poi_cnt
","arrive_poi_cnt
_last_cycle","""arrive_poi_cnt
""",amap_ad.dws_gd_ad_flow_effect_base_di,arrive_poi_cnt,20240606之后才上线这个指标，不可回溯,,,,,,,,,,,,,,,,,,,,
有效到店成本——>广告有效到店成本,"【广告域】里的——用户到店消耗
÷【广告域】里的——广告有效到店量",1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,是,是,无,CPC到店消耗/有效到店量——>用户到店消耗/有效到店量,CPC到店消耗/有效到店量,"arrive_settle_cost

_rate","arrive_settle_cost
_rate_last_cycle",sum(ad_cust_res_cost) / sum(ad_arrive_poi_cnt ),amap_ad.dws_gd_ad_flow_effect_base_di,settle_cost,-,,,,,,,,,,,,,,,,,,,,
来电量,【广告域】里的——广告来电量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,通过推广内容进入详情页并产生电话拨打并接通的个数（仅统计CPC到店的）,通过推广内容进入详情页并产生电话拨打并接通的个数,call_end_cnt,call_end_cnt_last_cycle,call_end_cnt,amap_ad.dws_gd_ad_flow_effect_base_di,orig_call_end_cnt,20240801之后用orig_call_end_cnt口径，20240801之前用call_end_cnt口径@陈擎霄(淮序),,,,,,,,,,,,,,,,,,,,
智能体数据,/,智能体曝光量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在门店内智能体模块的所有曝光pv，包含欢迎态、固定位等所有不同位置的去重曝光,智能体访问量 （pv）,"entry_exp_uv
",,,埋点取值,埋点梳理for智能体相关,,,,,,,,,,,,,,,,,,,,,
智能体使用量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,智能体访问量 （pv）,智能体总对话消息数，比如用户和智能体回复算作两条消息数,"entry_clk_uv
",,,埋点取值,,,,,,,,,,,,,,,,,,,,,
智能体会话量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,智能体总对话消息数，比如用户和智能体回复算作两条消息数,在智能体对话模块，用户与智能体之间进行的有效交互次数,"msg_cnt
",,,gd_info_biz_app.im_chat_detail," SELECT  shop_id
                        ,ds
                        ,1 as 是否有会话
                        ,SUM(cnt) AS 消息数
                        ,COUNT(uid) AS 消息发送用户数
                        ,COUNT(CASE    WHEN cnt = 1 THEN uid END) AS 会话次数_1轮对话
                        ,COUNT(CASE    WHEN cnt = 2 THEN uid END) AS 会话次数_2轮对话
                        ,COUNT(CASE    WHEN cnt = 3 THEN uid END) AS 会话次数_3轮对话
                        ,COUNT(CASE    WHEN cnt IN (4,5) THEN uid END) AS 会话次数_4_5轮对话
                        ,COUNT(CASE    WHEN cnt > 5 THEN uid END) AS 会话次数_超5轮对话
                FROM    (
                            SELECT  shop_id
                                    ,ds
                                    ,GET_JSON_OBJECT(infos,'$.userLocations[0].uid.appUid') AS uid
                                    ,COUNT(*) AS cnt
                            FROM    gd_info_biz_app.im_chat_detail
                            WHERE   GET_JSON_OBJECT(GET_JSON_OBJECT(GET_JSON_OBJECT(infos,'$.notificationInfo.content'),'$.msg.content'),'$.data') LIKE '%text%'
                            AND     SIZE(
                                 FROM_JSON(
                                           GET_JSON_OBJECT(GET_JSON_OBJECT(GET_JSON_OBJECT(infos,'$.notificationInfo.content'),'$.msg.content'),'$.data')
                                 ,'map<string, string>')
                            ) == 1
                            AND     SUBSTR(log_time,1,10) > '2024-09-01'
                            GROUP BY shop_id
                                     ,ds
                                     ,uid
                        ) 
                GROUP BY shop_id
                         ,ds",,,,,,,,,,,,,,,,,,,,,
平均有效对话轮次,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在智能体对话模块，用户与智能体之间进行的有效交互次数,在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等,"msg_cnt
_rate",,,总对话消息数/总uid,,,,,,,,,,,,,,,,,,,,,,
智能体导购次数,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等,用户点击智能体后，通过到店导航、打车到店、到店订单核销的用户行为次数之和,"leads_serve_card_exp_uv
",,,"1、留资
2、团购+订座+预约礼+电话预定+排队的弹出次数汇总，无需去重",埋点取值：埋点梳理for智能体相关,,,,,,,,,,,,,,,,,,,,,
留资卡片,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,,留资卡片的曝光uv,"leads_card_exp_uv
",,,,,,,,,,,,,,,,,,,,,,,,,
服务卡片,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,,团购+订座+预约礼+电话预定+排队的曝光uv,"serve_card_exp_uv
",,,,,,,,,,,,,,,,,,,,,,,,,
智能体引导到店数,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,用户点击智能体后，通过到店导航、打车到店、到店订单核销的用户行为次数之和,用户点击智能体后，首次访问当前门店且通过到店导航、打车到店、到店订单核销的用户行为次数之和,"arrive_20m_uv
",,,点击过智能体，且到店的人，到店取0-20m口径的数据,,,,,,,,,,,,,,,,,,,,,,
智能体新客引导到店数,-,,,高德,自定义，最长62天,"PID,门店",是,是,是,无,用户点击智能体后，首次访问当前门店且通过到店导航、打车到店、到店订单核销的用户行为次数之和,智能体引导到店数 / 有效到店量,,,,用户点击智能体后，首次访问当前门店，且到店量，到店取0-20m口径的数据,,,,,,,,,,,,,,,,,,,,,,
智能体留资占比,-,,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,高德,自定义，最长62天,"PID,门店",否,是,是,无,智能体留资量 /（有效客资量-平台客资）,行中成功语音播报的次数，指完整播放,"leads_card_exp_uv
_rate",,,"广告在投：在线咨询/（总留资量-平台客资）
非广告在投：智能体电话拨打量 / 电话拨打量",,,,,,,,,,,,,,,,,,,,,,
有效播报次数,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,行中成功语音播报的次数，指完整播放,,"voice_msg_cnt
",,,https://data.alibaba-inc.com/portal/report.htm?id=826089&fbiId=1547966&sectionType=push_to_read&spm=a312g.22291464.0.0%E8%BF%99%E4%B8%AA%E7%9C%8B%E6%9D%BF,,,,,,,,,,,,,,,,,,,,,,
喜报数据汇总for智能体,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
新增,,退款订单量,万强交易域新开发字段：退款订单量,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,退款订单金额,万强交易域新开发字段：退款订单金额,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,