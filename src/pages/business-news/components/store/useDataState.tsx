import { Form } from 'antd';
import { createContext, PropsWithChildren, useState, useContext } from 'react';
import { useAiAnalysis } from '@/pages/index/components/task-detail-drawer/useAiAnalysis';
import { useLocation } from 'ice';
import { unique } from 'radash';

export interface IField {
  title?: string;
  label?: string;
  key: string;
  defaultHidden?: boolean;
  children?: IField[];
  render?: () => React.ReactNode;
}

const flattenFields = (fields: IField[]): IField[] => {
  return fields.flatMap((item) => {
    return item.children ? [item, ...flattenFields(item.children)] : [item];
  });
};

function useData(props: { pid: any }) {
  const [form] = Form.useForm();
  const dateRange = Form.useWatch('dateRange', form);
  const shopList = Form.useWatch('shopList', form) || [];
  const hiddenFields = Form.useWatch('hiddenFields', form) || [];
  const [allShopsSelected, setAllShopsSelected] = useState(false);
  const shopName = shopList?.[0]?.label || '';
  const onHiddenFieldChange = (value) => {
    if (hiddenFields.includes(value)) {
      form.setFieldsValue({ hiddenFields: hiddenFields.filter((item) => item !== value) });
    } else {
      form.setFieldsValue({ hiddenFields: [...hiddenFields, value] });
    }
  };
  const shopIdList = shopList?.map((item) => item.value);

  const { aiSummary } = useAiAnalysis({
    pid: props.pid,
    shopIdList,
    dateRange,
    bizSource: 'business-news',
  });

  const [fields, setFields] = useState<IField[]>([]);
  const [isFood, setIsFood] = useState(false);
  const [peerRangeMap, setPeerRangeMap] = useState<Record<string, 'NORMAL' | 'CITY' | 'DISTRICT'>>(
    {},
  );
  const location = useLocation();
  const isChild = !location.pathname.includes('business-news');

  function registerField(field: IField) {
    const hiddenFields = form.getFieldValue('hiddenFields');
    let _hiddenFields = [...hiddenFields];
    const flattenAllFieldsList = flattenFields(fields);
    const flattenFieldList = flattenFields([field]);
    let flag = false;
    if (!flattenAllFieldsList.some((item) => item.key === field.key)) {
      setFields((prev) => [...prev, field]);
    } else {
      setFields((prev) => prev.map((item) => (item.key === field.key ? field : item)));
    }
    flattenFieldList.forEach((item) => {
      if (item.defaultHidden) {
        _hiddenFields.push(item.key);
        flag = true;
      }
    });
    _hiddenFields = unique(_hiddenFields);
    if (flag) {
      form.setFieldsValue({ hiddenFields: _hiddenFields });
    }
  }

  function unregisterField(keys: string | string[]) {
    let _hiddenFields = [...hiddenFields];
    if (Array.isArray(keys)) {
      _hiddenFields = _hiddenFields.filter((key) => !keys.includes(key));
      setFields((prev) => prev.filter((item) => !keys.includes(item.key)));
    } else {
      _hiddenFields = _hiddenFields.filter((key) => key !== keys);
      setFields((prev) => prev.filter((item) => item.key !== keys));
    }
    form.setFieldsValue({ hiddenFields: _hiddenFields });
  }

  function isShow(key: string): boolean;
  function isShow(key: string[]): boolean[];
  function isShow(key: string | string[]): boolean | boolean[] {
    if (Array.isArray(key)) {
      return key.map((item) => !hiddenFields.includes(item));
    }
    return !hiddenFields.includes(key);
  }

  const visibleFields = fields.filter((field) => {
    const flattenFieldList = flattenFields([field]);
    return flattenFieldList.some((item) => !hiddenFields.includes(item.key));
  });

  return {
    dateRange,
    shopIdList,
    shopName,
    hiddenFields,
    pid: props.pid,
    form,
    aiSummary,
    shopList,
    allShopsSelected,
    setAllShopsSelected,
    // 新增返回值
    fields,
    visibleFields, // 展示的字段
    registerField,
    unregisterField,
    isChild,
    isShow,
    isFood,
    setIsFood,
    onHiddenFieldChange,
    isMultiShops: shopIdList && shopIdList.length > 1,
    peerRangeMap,
    setPeerRange: (fieldKey: string, range: 'NORMAL' | 'CITY' | 'DISTRICT') => {
      const newPeerRangeMap = { ...peerRangeMap, [fieldKey]: range };
      setPeerRangeMap(newPeerRangeMap);
    },
    getPeerRange: (fieldKey: string) => peerRangeMap[fieldKey] || 'NORMAL',
  };
}

const Context = createContext<ReturnType<typeof useData>>({} as ReturnType<typeof useData>);

export function DateStateProvider(props: PropsWithChildren<{ pid: any }>) {
  const data = useData({ pid: props.pid });
  return <Context.Provider value={data}>{props.children}</Context.Provider>;
}

export const useDataState = () => useContext(Context);
