﻿模块,分类,数据指标,BC端中间层指标@崔峻(熊鹿)(崔峻),六夏标记-是否需要同行对比,小数位,端,时间周期（T+1）,汇总维度,多门店维度是否可加总,【商户通】门店+PID维度默认展示,【旺铺】门店+PID维度默认展示,对应经营参谋指标,字段说明（经营喜报小问号）,业务口径（内部一页纸）,喜报字段-服务端--值,喜报字段-服务端--环比,版本,接口人,"数仓底表（90d）对应字段
autonavi_base_sp.ads_gd_biz_shop_good_news_index_90d","v2表
autonavi_base_sp.ads_gd_biz_shop_index_detail_v2",最大可支持的时间跨度23年10月1日之后,,,,,,,,,,,,,,,,,,,
商家质量分,商家质量分,商家质量分,,1，离线数据，无同行,,高德,实时API,门店,否,是,是,,,高德商家门店质量分,business_score,,,齐冀,"autonavi_base_sp.dim_gd_ctt_poi_index(500天)
需要shopid和poiid转换",,最新的,,,,,,,,,,,,,,,,,,,
门店信息守护,/,防干扰（天）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,是,是,是,无,,,wp_online_days,,,,,,,,,,,,,,,,,,,,,,,,,
累计年费在约（天）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,否,是,是,无,,,wp_online_days_total,,,,,,,,,,,,,,,,,,,,,,,,,
信息安全守护（次）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,门店,是,是,是,累计用户反馈（经营参谋app端）,,,security_guards_cnt,,,,,,,,,,,,,,,,,,,,,,,,,
门店经营数据,/,曝光量——>门店曝光量（问号文案需要修改）,【分发域】里的——门店曝光量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,"曝光量
exposure_pv",在高德app上门店维度的曝光pv,顾客在高德地图上看到门店的次数（包含顾客使用地图、搜索等功能时看到该店的次数）,exposure_pv,exposure_pv_last_cycle,,齐冀、崔峻,"90d（90天）.exp_pv
amap_ad.dws_gd_ad_flow_effect_base_di.valid_exposure_cnt",v2.exposure_pv,"广告表可以回溯15个月@陈擎霄(淮序)
曝光指标20220801之后 OK 有开发量",,,,,,,,,,,,,,,,,,,
访问量——>门店访问量,【分发域】里的——门店访问量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,"访问量
store_view_pv",用户使用高德app导航到店的人次,顾客在高德地图发起店铺导航的数量,store_view_pv,store_view_pv_last_cycle,,齐冀、崔峻,"90d.detail_pv
amap_ad.dws_gd_ad_flow_effect_base_di.settle_click_cnt",v2.store_view_pv,（近似计算@崔峻(熊鹿)(崔峻)）,"select 
        poiid,
        dpv_poi_cnt as store_view_pv
    from 
    autonavi_base_sp.ads_gd_info_poi_features_di
    where ds = '${date}'",,,,,,,,,,,,,,,,,,
商家相册数,【商家域】里的——相册图片数量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,"PID,门店",是,是,是,无,商家上传的相册数,,shop_pic_cnt,,,,,,,,,,,,,,,,,,,,,,,,,
菜品数——>商家招牌菜数,【商家域】里的——招牌菜推荐菜数量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,,"PID,门店",是,是,否,无,商家上传的推荐菜数,,shop_dish_cnt,,,,,,,,,,,,,,,,,,,,,,,,,
驾车导航量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,否,驾车导航量,通过驾车方式（驾车、新能源、货车、摩托车）发起路线规划、导航的总量（终点为当前poi）,通过驾车方式（驾车、新能源、货车、摩托车）发起路线规划、导航的总量（终点为当前poi）,,,,,,,,,,,,,,,,,,,,,,,,,,
规划到店量（数据本身有问题，无需付费的商家没统计到。指标现在小二复盘时没有用）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,否,"导航到店量
total_route_pv","驾车、公共交通、骑行、步行等方式发起门店路线规划总量
顾客在高德地图发起店铺的所有出行方式（含打车）的路线规划、导航的数量（终点为当前poi）",驾车、公共交通、骑行、步行等方式发起门店路线规划总量,gd_navi_pv,gd_navi_pv_last_cycle,,崔峻,90d.navi_pv,v2.total_route_pv,（近似计算@崔峻(熊鹿)(崔峻)）,autonavi_base_sp.dwd_gd_info_route_eta_di 这个表只有186天,,,,,,,,,,,,,,,,,,
预约到店量,"【流量埋点】里的——电话拨打量
+【交易域】里的——在线订座量
+【交易域】里的——商品下单量",1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,预约到店量,用户在高德产生的预约到店量=电话预订量+在线预订量+团购订单,凭证订单量=电话预订+在线预订+团购订单，1）电话预订量=电话预订，2）在线预订总订单=在线订座（eta订座+小程序订座）+在线排队（eta排队+小程序排队）+到店礼，3）团购总订单=团单（券包+代金券）+买单+自提,shop_order_cnt,shop_order_cnt_last_cycle,,崔峻,90d.food_order_cnt,v2.food_order_cnt,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
电话预订量——>电话拨打量,改名为电话拨打量，【流量域】-电话拨打量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,电话预订量,通过高德平台接通的总电话量,通过高德平台接通的总电话量,"phone_call_order_cnt


","phone_call_order_cnt


_last_cycle",,崔峻,90d.food_phone_order_cnt,v2.food_phone_order_cnt,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
在线总预订量（这个指标删掉）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,在线预订量,在线预订总订单=在线订座+在线排队+到店礼,在线预订总订单=在线订座（eta订座+小程序订座）+在线排队（eta排队+小程序排队）+到店礼,online_order_cnt,online_order_cnt_last_cycle,,崔峻,90d.food_booking_total_cnt,v2.food_booking_total_cnt,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
在线订座量,【交易域】里的——在线订座量,1,,,,,,,,,,,booking_cnt,booking_cnt_last_cycle,,,,,,,,,,,,,,,,,,,,,,,,
电话拨打量（等于上面的电话拨打量，重复指标删除）,,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,拨打门店电话的次数（不限制必须接通）【目前已有poi的电话量】,拨打门店电话的次数（不限制必须接通）【目前已有poi的电话量】,call_phone_cnt,,,,90d.call_phone_cnt,,,,,,,,,,,,,,,,,,,,,
电话接通率,电话接通量/电话拨打量,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,是,否,预订电话接通率,电话预订量/电话拨打量,电话预订量/电话拨打量——>电话接通量/电话拨打量,phone_order_rate,phone_order_rate_last_cycle,,-,90d.food_phone_order_cnt / food_phone_order_rate ,,-,,,,,,,,,,,,,,,,,,,
在线订座接单率,"【交易域】里的——在线订座接单量
÷【交易域】里的——在线订座量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,在线订座接单率,在线订座接单/在线订座下单,在线订座接单/在线订座下单,booking_rate,booking_rate_last_cycle,,-,服务自己算,,-,,,,,,,,,,,,,,,,,,,
团购核销率——>商品核销率,"【交易域】里的——商品核销量
÷【交易域】里的——商品下单量",1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,否,否,无,团单核销量/团单下单量,团单核销量/团单下单量,used_order_rate,used_order_ast_cycle,,-,服务自己算,,-,,,,,,,,,,,,,,,,,,,
团购总订单量——>商品下单量,【交易域】里的——商品下单量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,否,否,团购总订单,团购总订单=团单（券包+代金券）+买单+自提,团购总订单=团单（券包+代金券）+买单+自提,order_cnt,order_cnt_last_cycle,,崔峻,90d.food_groupbuy_total_cnt,v2.food_groupbuy_total_cnt,全量+999的口碑表,,,,,,,,,,,,,,,,,,,
团单下单量（只有团购）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,无,用户在高德平台团单的下单量,用户在高德平台团单的下单量,group_order_cnt,group_order_cnt_last_cycle,,崔峻,90d.food_groupbuy_total_cnt,v2.food_groupbuy_total_cnt,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
到店买单量（现在高德C端没有买单业务）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,无,用户实际到店买单量,用户实际到店买单,pay_order_cnt,pay_order_cnt_last_cycle,,崔峻,90d.food_payment_order_cnt,,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
自提订单量（现在高德C端没有自提业务）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,否,无,用户在高德平台下单并支付的自提订单量,用户在高德平台下单并支付的自提订单,pickup_order_cnt,pickup_order_cnt_last_cycle,,崔峻,90d.food_pickup_order_cnt,,全量表 OK 有开发量,,,,,,,,,,,,,,,,,,,
广告投放,/,广告总消耗,【广告域】里的——广告总消耗,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,总广告消耗（含现金、红包消耗），用户到店+客资线索,通过广告投放，用户点击所产生的消耗金额（含现金、红包消耗）,settle_cost,settle_cost_last_cycle,,齐冀,amap_ad.dws_gd_ad_flow_effect_base_di.settle_cost或amap_ad.dwd_gd_ad_settle_cost_all_di,,可回溯15个月,,,,,,,,,,,,,,,,,,,
有效到店量——>广告有效到店量,【广告域】里的——广告有效到店量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,用户到店广告投放后，通过到店导航、打车到店、到店订单核销的用户行为次数之和,广告投放后，有效到您门店的顾客数，具体包括通过到店导航、到店打车、到店订单核销的行为次数之和,arrive_poi_cnt,arrive_poi_cnt_last_cycle,,齐冀,amap_ad.dws_gd_ad_flow_effect_base_di.arrive_poi_cnt,,20240701之后才上线这个指标，不可回溯,,,,,,,,,,,,,,,,,,,
有效到店成本——>广告有效到店成本,【广告域】里的——广告有效到店成本,1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,是,是,无,CPC到店消耗/有效到店量,CPC到店消耗/有效到店量,"arrive_settle_cost

_rate","arrive_settle_cost
_rate_last_cycle",,-,服务自己算,,-,, sum(settle_cost) / sum(arrive_poi_cnt) ,,,,,,,,,,,,,,,,,
广告曝光量（问号文案需要修改）,【广告域】里的——广告曝光PV,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,广告组件的曝光量（单门店下多个广告组件累计曝光量）,投放到店通广告增加的门店被看见次数,valid_exposure_cnt,valid_exposure_cnt_last_cycle,,齐冀,amap_ad.dws_gd_ad_flow_effect_base_di.valid_exposure_cnt,,可回溯15个月,,,,,,,,,,,,,,,,,,,
广告点击量,【广告域】里的——广告点击量,1,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",是,是,是,无,广告组件的访问量（单门店下多个广告组件累计点击量）,投放到店通广告增加的门店被访问次数,"valid_click_cnt

","valid_click_cnt
_last_cycle",,齐冀,amap_ad.dws_gd_ad_flow_effect_base_di.settle_click_cnt,,@李亮(秃贝)改成valid_click_cnt，可回溯15个月；@陈擎霄(淮序),,,,,,,,,,,,,,,,,,,
广告点击率,广告点击量/广告曝光量,1,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,多端,自定义，最长62天,"PID,门店",否,是,是,无,广告点击量/广告曝光量,广告点击量/广告曝光量,valid_click_rate,valid_click_rate_last_cycle,,-,服务自己算,,-,,ad_clk_pv/ad_exp_pv,,,,,,,,,,,,,,,,,
预约到店成本——>广告预约到店成本？,"【广告域】里的——广告总消耗
÷【交易域】里的——预约到店量

（预约到店量是个应用层指标，中间层不一定做-0701记录）",1,两位小数，超过1万的，“万”为单位保留1位小数。例如1.9万,多端,自定义，最长62天,"PID,门店",否,是,是,无,广告总消耗/预约到店量,广告总消耗/预约到店量,settle_cost_rate,settle_cost_last_cycle,,-,服务自己算,,,,,,,,,,,,,,,,,,,,,
智能体数据,/,智能体曝光量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在门店内智能体模块的所有曝光pv，包含欢迎态、固定位等所有不同位置的去重曝光,智能体访问量 （pv）,"entry_exp_uv

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
智能体使用量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,智能体访问量 （pv）,智能体总对话消息数，比如用户和智能体回复算作两条消息数,"entry_clk_uv

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,
智能体会话量,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,智能体总对话消息数，比如用户和智能体回复算作两条消息数,在智能体对话模块，用户与智能体之间进行的有效交互次数,"msg_cnt

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
平均有效对话轮次,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在智能体对话模块，用户与智能体之间进行的有效交互次数,在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等,"msg_cnt
_rate
",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
智能体导购次数,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,在智能体对话内，发生的有效转化推荐次数，包括团购、订座、留资等,用户点击智能体后，通过到店导航、打车到店、到店订单核销的用户行为次数之和。等于留资卡片+服务卡片,"leads_serve_card_exp_uv

","
",25.3新增,,,,,,,,,,,,,,,,,,,,,,,
留资卡片——>留资卡片曝光UV（中间层加，页面不加）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,,留资卡片的曝光uv,"leads_card_exp_uv

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
服务卡片——>服务卡片曝光UV（中间层加，页面不加）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,,团购+订座+预约礼+电话预定+排队的曝光uv,"serve_card_exp_uv

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
引导到店数——>智能体引导到店数（中间层加，页面不加）,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,用户点击智能体后，通过到店导航、打车到店、到店订单核销的用户行为次数之和,用户点击智能体后，首次访问当前门店且通过到店导航、打车到店、到店订单核销的用户行为次数之和,"arrive_20m_uv

",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
新客引导到店数,-,,,高德,自定义，最长62天,"PID,门店",是,是,是,无,用户点击智能体后，首次访问当前门店且通过到店导航、打车到店、到店订单核销的用户行为次数之和,智能体引导到店数 / 有效到店量,,,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
引导到店占有率,-,,百分比，服务端保留4位，百分后展示两位小数，比如0.01%,高德,自定义，最长62天,"PID,门店",否,是,是,无,智能体引导到店数 / 有效到店量,行中成功语音播报的次数，指完整播放,"arrive_20m_uv_rate


",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
有效播报次数,-,,正整数，超过1万的，“万”为单位保留1位小数。例如1.9万,高德,自定义，最长62天,"PID,门店",是,是,是,无,行中成功语音播报的次数，指完整播放,,"voice_msg_cnt
",,25.3新增,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,